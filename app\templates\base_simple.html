<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}小红书文案管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons - 使用最新版本确保图标完整 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- 自定义样式 -->
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 确保Bootstrap Icons正确加载 - 备用字体定义 */
        @font-face {
            font-family: "bootstrap-icons";
            src: url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2") format("woff2"),
                 url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff") format("woff");
        }

        /* 菜单图标样式强化 */
        .sidebar .nav-link i[class*="bi-"] {
            font-family: "bootstrap-icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            display: inline-block !important;
            width: 1.2em !important;
            text-align: center !important;
        }

        /* 菜单专用图标定义 - 确保显示 */
        .bi-speedometer2::before { content: "\f58c" !important; }
        .bi-layer-group::before { content: "\f46a" !important; }
        .bi-people::before { content: "\f4dc" !important; }
        .bi-pencil-square::before { content: "\f4d0" !important; }
        .bi-clipboard-check::before { content: "\f28f" !important; }
        .bi-image::before { content: "\f3f5" !important; }
        .bi-check2-square::before { content: "\f26f" !important; }
        .bi-person-check::before { content: "\f4d6" !important; }
        .bi-send::before { content: "\f52c" !important; }
        .bi-list-check::before { content: "\f477" !important; }
        .bi-person-gear::before { content: "\f4d8" !important; }
        .bi-gear::before { content: "\f3e2" !important; }
        .bi-box-arrow-right::before { content: "\f1c4" !important; }

        /* 备用方案：如果Bootstrap Icons字体加载失败，使用Unicode符号 */
        .sidebar .nav-link i.bi-speedometer2::before { content: "📊" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-layer-group::before { content: "📋" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-people::before { content: "👥" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-pencil-square::before { content: "✏️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-clipboard-check::before { content: "✅" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-image::before { content: "🖼️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-check2-square::before { content: "✔️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-person-check::before { content: "👤" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-send::before { content: "📤" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-list-check::before { content: "📝" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-person-gear::before { content: "⚙️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-gear::before { content: "🔧" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-box-arrow-right::before { content: "🚪" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }

        /* 标记美化样式 */
        .template-mark {
            display: inline-block;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            color: #1565c0;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 600;
            margin: 0 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
            border: 1px solid #bbdefb;
            position: relative;
        }

        .template-mark::before {
            content: '🏷️';
            margin-right: 4px;
            font-size: 0.9em;
        }

        .template-mark:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.12);
            background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
        }

        /* 模板标题行样式 */
        .template-title {
            line-height: 1.6;
            max-width: 300px;
            word-wrap: break-word;
        }

        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0;
        }

        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        .main-content {
            min-height: 100vh;
            padding: 0;
        }

        .page-content {
            display: none;
            padding: 20px;
        }

        .page-content.active {
            display: block;
        }

        .top-bar {
            background-color: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            margin-bottom: 0;
        }

        .breadcrumb {
            margin-bottom: 0;
            background-color: transparent;
        }

    /* 右上角提示框样式 */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }

    .custom-toast {
        min-width: 300px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left: 4px solid #28a745;
        animation: slideInRight 0.3s ease-out;
    }

    .custom-toast.error {
        border-left-color: #dc3545;
    }

    .custom-toast.warning {
        border-left-color: #ffc107;
    }

    .custom-toast.info {
        border-left-color: #17a2b8;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .custom-toast.hiding {
        animation: slideOutRight 0.3s ease-in;
    }

    .toast-header {
        background: transparent;
        border-bottom: 1px solid #dee2e6;
        padding: 8px 12px;
    }

    .toast-body {
        padding: 12px;
        color: #495057;
    }

    .toast-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #6c757d;
        cursor: pointer;
        padding: 0;
        margin-left: auto;
    }

    .toast-close:hover {
        color: #495057;
    }

    /* 用户信息区域样式 */
    .user-info-section {
        border-top: 1px solid #e9ecef;
        border-bottom: 1px solid #e9ecef;
    }

    .user-info-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
    }

    .user-info-card:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .user-details .username {
        line-height: 1.2;
        margin-bottom: 2px;
    }

    .user-details .user-role {
        line-height: 1.1;
        font-style: italic;
    }

    .user-info-card .bi-person-circle {
        color: #0d6efd !important;
        flex-shrink: 0;
    }
    </style>

    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 右上角提示框容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="container-fluid">
        <div class="row">
            <!-- 左侧菜单 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>功能菜单</span>
                    </h6>
                    <ul class="nav flex-column">
                        {% if current_user.is_authenticated %}
                            {% for menu_item in current_user.get_menu_items() %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ menu_item.url }}">
                                    <i class="{{ menu_item.icon or 'bi bi-link' }}"></i> {{ menu_item.name }}
                                </a>
                            </li>
                            {% endfor %}
                        {% endif %}
                    </ul>

                    <!-- 用户信息区域 -->
                    {% if current_user.is_authenticated %}
                    <div class="user-info-section px-3 mt-4 mb-3">
                        <div class="user-info-card p-3 bg-light rounded">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-person-circle me-2 text-primary" style="font-size: 1.2em;"></i>
                                <div class="user-details">
                                    <div class="username fw-bold text-dark" style="font-size: 0.9em;">
                                        {{ current_user.username }}
                                    </div>
                                    <div class="user-role text-muted" style="font-size: 0.8em;">
                                        {{ current_user.real_name or '未设置角色' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统设置</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <!-- 顶部面包屑 -->
                <div class="top-bar">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main_simple.dashboard') }}">首页</a></li>
                            <li class="breadcrumb-item active" aria-current="page" id="current-page-title">控制台</li>
                        </ol>
                    </nav>
                </div>

                <!-- 页面内容容器 -->
                <div id="page-container">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS - 使用简化版本 -->
    <script src="{{ url_for('static', filename='js/bootstrap-simple.min.js') }}"></script>
    
    <!-- 简化的页面切换系统 -->
    <script>
        // 页面配置 - 所有URL都指向简化后台路由
        const pageConfig = {
            'dashboard': { title: '控制台', url: '/simple/dashboard' },
            'templates': { title: '模板管理', url: '/simple/templates' },
            'clients': { title: '客户管理', url: '/simple/clients' },
            'content': { title: '内容生成', url: '/simple/content' },
            'review-content': { title: '初审文案', url: '/simple/review-content' },
            'image-upload': { title: '图片上传', url: '/simple/image-upload' },
            'final-review': { title: '最终审核', url: '/simple/final-review' },
            'client-review': { title: '客户审核', url: '/simple/client-review' },
            'publish-manage': { title: '发布管理', url: '/simple/publish-manage' },
            'publish-status-manage': { title: '发布状态管理', url: '/simple/publish-status-manage' },
            'users': { title: '用户管理', url: '/simple/users' },
            'system': { title: '系统设置', url: '/simple/system' }
        };

        // 当前活动页面
        let currentPage = 'dashboard';

        // 页面初始化 - 检测当前页面并设置正确的状态
        function initializePage() {
            // 从URL路径检测当前页面
            const path = window.location.pathname;
            const pageMatch = path.match(/\/simple\/([^\/]+)/);

            if (pageMatch) {
                const detectedPage = pageMatch[1];
                console.log('检测到当前页面:', detectedPage);

                // 更新当前页面状态
                currentPage = detectedPage;

                // 更新菜单激活状态
                updateMenuState(detectedPage);

                // 更新面包屑
                updateBreadcrumb(detectedPage);
            }
        }

        // 全局标记处理函数
        window.processTemplateMarks = function() {
            console.log('开始处理模板标记...');
            const titleElements = document.querySelectorAll('.template-title');
            console.log('找到标题元素数量:', titleElements.length);

            titleElements.forEach(function(titleElement) {
                let title = titleElement.innerHTML;
                console.log('处理标题:', title);

                // 如果已经包含 template-mark 标签，跳过处理
                if (title.includes('template-mark')) {
                    console.log('跳过已格式化的标题');
                    return;
                }

                // 保持 {标记名} 格式并添加美化样式
                title = title.replace(/\{([^}]+)\}/g, function(match, markName) {
                    console.log('找到标记:', markName);
                    // 使用统一的标记样式，保持{}格式
                    return `<span class="template-mark">{${markName}}</span>`;
                });

                titleElement.innerHTML = title;
            });
        };

        // 显示指定页面 - 直接跳转到完整页面
        function showPage(pageId) {
            console.log('跳转到页面:', pageId);
            
            const config = pageConfig[pageId];
            if (config) {
                window.location.href = config.url;
            }
        }

        // 更新菜单激活状态
        function updateMenuState(pageId) {
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // 根据pageId找到对应的URL
            const config = pageConfig[pageId];
            if (config) {
                // 查找匹配URL的菜单链接
                const activeLink = document.querySelector(`.sidebar .nav-link[href="${config.url}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                    console.log('激活菜单:', config.title, config.url);
                } else {
                    console.warn('未找到菜单链接:', config.url);
                }
            }
        }

        // 更新面包屑
        function updateBreadcrumb(pageId) {
            const config = pageConfig[pageId];
            if (config) {
                document.getElementById('current-page-title').textContent = config.title;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL确定当前页面
            const path = window.location.pathname;
            let initialPage = 'dashboard';

            // 从路径中提取页面名称
            const pathParts = path.split('/');
            if (pathParts.length >= 3 && pathParts[1] === 'simple') {
                const pageName = pathParts[2];
                if (pageConfig[pageName]) {
                    initialPage = pageName;
                }
            }

            console.log('页面初始化，当前页面:', initialPage, '路径:', path);

            // 设置当前页面状态
            currentPage = initialPage;

            // 更新导航菜单状态
            updateMenuState(initialPage);

            // 更新面包屑
            updateBreadcrumb(initialPage);

            // 如果是模板页面，处理标记美化
            if (initialPage === 'templates') {
                setTimeout(function() {
                    if (typeof window.processTemplateMarks === 'function') {
                        console.log('页面加载后处理模板标记美化...');
                        window.processTemplateMarks();
                    }
                }, 100);
            }

            // 初始化菜单点击事件
            initMenuClickEvents();
        });

        // 初始化菜单点击事件
        function initMenuClickEvents() {
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                // 跳过退出登录链接
                if (link.href.includes('/auth/logout')) {
                    return;
                }

                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const href = this.getAttribute('href');
                    console.log('菜单点击:', href);

                    // 查找对应的页面ID
                    let targetPageId = null;
                    for (const [id, config] of Object.entries(pageConfig)) {
                        if (config.url === href) {
                            targetPageId = id;
                            break;
                        }
                    }

                    if (targetPageId) {
                        showPage(targetPageId);
                        // 更新URL但不刷新页面
                        history.pushState({page: targetPageId}, '', href);
                    } else {
                        // 如果没有找到对应的页面配置，直接跳转
                        console.log('未找到页面配置，直接跳转:', href);
                        window.location.href = href;
                    }
                });
            });
        }

        // 处理浏览器前进后退
        window.addEventListener('popstate', function(event) {
            if (event.state && event.state.page) {
                showPage(event.state.page);
            }
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
